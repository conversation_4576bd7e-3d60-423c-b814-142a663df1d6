<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片预览功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .attachment-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #ebeef5;
            border-radius: 6px;
            background: #fafafa;
            margin-bottom: 10px;
        }
        .attachment-icon {
            margin-right: 12px;
            font-size: 20px;
            color: #67C23A;
        }
        .attachment-info {
            flex: 1;
            min-width: 0;
        }
        .attachment-name {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .attachment-size {
            font-size: 12px;
            color: #909399;
        }
        .attachment-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .btn {
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
        }
        .btn-primary {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .preview-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .preview-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            max-height: 80vh;
            overflow: auto;
        }
        .preview-image {
            max-width: 100%;
            max-height: 600px;
            object-fit: contain;
        }
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>履职记录详情 - 图片预览功能测试</h2>
        
        <h4>相关附件</h4>
        
        <!-- 模拟图片附件 -->
        <div class="attachment-item">
            <div class="attachment-icon">📷</div>
            <div class="attachment-info">
                <div class="attachment-name">现场照片1.jpg</div>
                <div class="attachment-size">2.5 MB</div>
            </div>
            <div class="attachment-actions">
                <button class="btn btn-primary" onclick="previewImage('https://via.placeholder.com/800x600/67C23A/ffffff?text=现场照片1')">预览</button>
                <button class="btn" onclick="downloadFile('现场照片1.jpg')">下载</button>
            </div>
        </div>
        
        <!-- 模拟另一个图片附件 -->
        <div class="attachment-item">
            <div class="attachment-icon">📷</div>
            <div class="attachment-info">
                <div class="attachment-name">活动记录.png</div>
                <div class="attachment-size">1.8 MB</div>
            </div>
            <div class="attachment-actions">
                <button class="btn btn-primary" onclick="previewImage('https://via.placeholder.com/600x400/409EFF/ffffff?text=活动记录')">预览</button>
                <button class="btn" onclick="downloadFile('活动记录.png')">下载</button>
            </div>
        </div>
        
        <!-- 模拟非图片附件 -->
        <div class="attachment-item">
            <div class="attachment-icon" style="color: #E6A23C;">🎵</div>
            <div class="attachment-info">
                <div class="attachment-name">会议录音.mp3</div>
                <div class="attachment-size">15.2 MB</div>
            </div>
            <div class="attachment-actions">
                <button class="btn" onclick="downloadFile('会议录音.mp3')">下载</button>
            </div>
        </div>
    </div>
    
    <!-- 图片预览模态框 -->
    <div id="previewModal" class="preview-modal">
        <div class="preview-content">
            <button class="close-btn" onclick="closePreview()">&times;</button>
            <h3>图片预览</h3>
            <img id="previewImage" class="preview-image" alt="预览图片">
        </div>
    </div>
    
    <script>
        function previewImage(imageUrl) {
            const modal = document.getElementById('previewModal');
            const img = document.getElementById('previewImage');
            
            img.src = imageUrl;
            modal.style.display = 'block';
        }
        
        function closePreview() {
            const modal = document.getElementById('previewModal');
            modal.style.display = 'none';
        }
        
        function downloadFile(filename) {
            alert('下载文件: ' + filename);
        }
        
        // 点击模态框背景关闭
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePreview();
            }
        });
        
        // ESC键关闭预览
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePreview();
            }
        });
    </script>
</body>
</html>
