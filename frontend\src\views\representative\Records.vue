<template>
  <div class="records-container">
    <div class="page-header">
      <h2>履职记录管理</h2>
      <p>记录和管理您的履职活动</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索履职内容、地点等"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
            @clear="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="履职类型">
          <el-select v-model="searchForm.performance_type" placeholder="请选择类型" clearable style="width: 150px" @change="handleSearch">
            <el-option v-for="type in performanceTypes" :key="type.value" :label="type.label" :value="type.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="履职状态">
          <el-select v-model="searchForm.performance_status" placeholder="请选择状态" clearable style="width: 120px" @change="handleSearch">
            <el-option v-for="status in performanceStatus" :key="status.value" :label="status.label" :value="status.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
            @change="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        新增记录
      </el-button>
      <div class="toolbar-right">
        <el-button @click="loadRecords">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 履职记录列表 -->
    <el-card class="table-card">
      <el-table 
        :data="recordsList" 
        v-loading="loading" 
        stripe 
        :height="'100%'"
        :flexible="true"
      >
        <el-table-column prop="performance_date" label="日期" width="120" sortable />
        <el-table-column prop="content_preview" label="履职内容" min-width="300" show-overflow-tooltip />
        <el-table-column prop="performance_type" label="履职类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.performance_type)">{{ row.performance_type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="activity_location" label="地点" width="150" show-overflow-tooltip />
        <el-table-column prop="performance_status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.performance_status)">
              {{ row.performance_status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="has_attachments" label="附件" width="80" align="center">
          <template #default="{ row }">
            <el-icon v-if="row.has_attachments" color="#409EFF" size="16"><Paperclip /></el-icon>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="最后更新时间" width="160" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" @click="viewRecord(row)">
                查看
              </el-button>
              <el-button size="small" type="primary" @click="editRecord(row)">
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="deleteRecord(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '新增履职记录' : '编辑履职记录'"
      width="800px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="recordForm"
        :rules="formRules"
        label-width="100px"
        size="large"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="履职日期" prop="performance_date">
              <el-date-picker
                v-model="recordForm.performance_date"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="履职类型" prop="performance_type">
              <el-select v-model="recordForm.performance_type" placeholder="请选择履职类型" style="width: 100%">
                <el-option v-for="type in performanceTypes" :key="type.value" :label="type.label" :value="type.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="履职内容" prop="performance_content">
          <el-input
            v-model="recordForm.performance_content"
            type="textarea"
            :rows="3"
            placeholder="请详细描述履职活动内容"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动地点" prop="activity_location">
              <el-input
                v-model="recordForm.activity_location"
                placeholder="请输入活动地点"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="履职状态" prop="performance_status">
              <el-select v-model="recordForm.performance_status" placeholder="请选择状态" style="width: 100%">
                <el-option v-for="status in performanceStatus" :key="status.value" :label="status.label" :value="status.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="详细描述" prop="detailed_description">
          <el-input
            v-model="recordForm.detailed_description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述履职活动的具体情况、收获等（可选）"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <!-- 附件上传组件 -->
        <div v-if="recordForm.id" class="attachment-section">
          <AttachmentUploader
            :record-id="recordForm.id"
            :initial-attachments="recordForm.attachments || []"
            @attachment-change="handleAttachmentChange"
            @upload-success="handleUploadSuccess"
            @upload-error="handleUploadError"
          />
        </div>
        <div v-else class="attachment-tip">
          <el-alert
            title="提示"
            description="保存履职记录后可以上传相关附件（照片、录音、视频、文档等）"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveRecord" :loading="saving">
          {{ dialogMode === 'add' ? '保存' : '更新' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="履职记录详情"
      width="800px"
    >
      <div v-if="currentRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="履职日期">
            {{ currentRecord.performance_date }}
          </el-descriptions-item>
          <el-descriptions-item label="履职类型">
            <el-tag :type="getTypeColor(currentRecord.performance_type)">
              {{ currentRecord.performance_type }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="活动地点">
            {{ currentRecord.activity_location }}
          </el-descriptions-item>
          <el-descriptions-item label="履职状态">
            <el-tag :type="getStatusColor(currentRecord.performance_status)">
              {{ currentRecord.performance_status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="履职内容" :span="2">
            <div class="content-text">{{ currentRecord.content_preview || currentRecord.performance_content }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="详细描述" :span="2" v-if="currentRecord.detailed_description">
            <div class="description-text">{{ currentRecord.detailed_description }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(currentRecord.created_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 附件展示 -->
        <div v-if="currentRecord.attachments && currentRecord.attachments.length > 0" class="attachments-section">
          <h4>相关附件</h4>

          <!-- 图片附件直接展示 -->
          <div v-if="imageAttachments.length > 0" class="images-section">
            <h5>现场照片</h5>
            <div class="images-grid">
              <div v-for="attachment in imageAttachments" :key="attachment.id" class="image-item">
                <div class="image-container" @click="previewImage(attachment)">
                  <img
                    v-if="attachment.previewUrl"
                    :src="attachment.previewUrl"
                    :alt="attachment.original_filename"
                    class="attachment-image"
                    @error="onImageError(attachment)"
                  />
                  <div v-else-if="attachment.loading" class="image-loading">
                    <el-icon class="is-loading" size="30"><Loading /></el-icon>
                    <p>加载中...</p>
                  </div>
                  <div v-else class="image-error">
                    <el-icon size="30"><Picture /></el-icon>
                    <p>加载失败</p>
                  </div>
                </div>
                <div class="image-info">
                  <div class="image-name" :title="attachment.original_filename">
                    {{ attachment.original_filename }}
                  </div>
                  <div class="image-actions">
                    <el-button size="small" @click.stop="downloadAttachment(attachment)">下载</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 其他类型附件 -->
          <div v-if="otherAttachments.length > 0" class="other-attachments">
            <h5>其他附件</h5>
            <div class="attachments-list">
              <div v-for="attachment in otherAttachments" :key="attachment.id" class="attachment-item">
                <div class="attachment-icon">
                  <el-icon v-if="attachment.file_type === 'audio'" color="#E6A23C"><Microphone /></el-icon>
                  <el-icon v-else-if="attachment.file_type === 'video'" color="#409EFF"><VideoCamera /></el-icon>
                  <el-icon v-else color="#909399"><Document /></el-icon>
                </div>
                <div class="attachment-info">
                  <div class="attachment-name" :title="attachment.original_filename">
                    {{ attachment.original_filename }}
                  </div>
                  <div class="attachment-size">{{ attachment.file_size_display }}</div>
                </div>
                <div class="attachment-actions">
                  <el-button size="small" @click="downloadAttachment(attachment)">下载</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      title="图片预览"
      width="800px"
      append-to-body
    >
      <div class="preview-container">
        <img
          :src="previewImageUrl"
          alt="预览图片"
          style="width: 100%; max-height: 600px; object-fit: contain;"
          @load="onImageLoadSuccess"
          @error="onImageLoadError"
          v-show="!imageLoading"
        />
        <div v-if="imageLoading" class="loading-overlay">
          <el-icon class="is-loading" size="50"><Loading /></el-icon>
          <p style="margin-top: 10px;">正在加载图片...</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Paperclip, Picture, Microphone, VideoCamera, Document, Loading } from '@element-plus/icons-vue'
import { performanceAPI, performanceChoicesAPI, performanceFileAPI } from '@/api/modules/performance'
import AttachmentUploader from '@/components/performance/AttachmentUploader.vue'

// 数据状态
const loading = ref(false)
const saving = ref(false)
const recordsList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  performance_type: '',
  performance_status: '',
  dateRange: [],
  search: ''
})

// 选择项数据
const performanceTypes = ref([])
const performanceStatus = ref([])

// 对话框状态
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit'

// 表单引用
const formRef = ref()

// 当前记录
const currentRecord = ref(null)

// 图片预览状态
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')
const previewLoading = ref(null) // 当前正在加载预览的附件ID
const imageLoading = ref(false)

// 表单数据
const recordForm = reactive({
  id: null,
  performance_date: '',
  performance_type: '',
  performance_content: '',
  activity_location: '',
  detailed_description: '',
  performance_status: '已完成',
  attachments: []
})

// 表单验证规则
const formRules = {
  performance_date: [
    { required: true, message: '请选择履职日期', trigger: 'change' }
  ],
  performance_type: [
    { required: true, message: '请选择履职类型', trigger: 'change' }
  ],
  performance_content: [
    { required: true, message: '请输入履职内容', trigger: 'blur' },
    { min: 2, max: 500, message: '履职内容长度在 2 到 500 个字符', trigger: 'blur' }
  ],
  activity_location: [
    { required: true, message: '请输入活动地点', trigger: 'blur' },
    { max: 200, message: '活动地点不能超过200个字符', trigger: 'blur' }
  ],
  performance_status: [
    { required: true, message: '请选择履职状态', trigger: 'change' }
  ]
}

// 计算属性
const searchParams = computed(() => {
  const params = {
    page: currentPage.value,
    page_size: pageSize.value,
    ordering: '-updated_at'  // 默认按最后更新时间降序排序
  }
  
  if (searchForm.performance_type) {
    params.performance_type = searchForm.performance_type
  }
  
  if (searchForm.performance_status) {
    params.performance_status = searchForm.performance_status
  }
  
  if (searchForm.dateRange && searchForm.dateRange.length === 2) {
    params.start_date = searchForm.dateRange[0]
    params.end_date = searchForm.dateRange[1]
  }
  
  if (searchForm.search) {
    params.search = searchForm.search
  }
  
  return params
})

// 分离图片和其他附件
const imageAttachments = computed(() => {
  if (!currentRecord.value?.attachments) return []
  return currentRecord.value.attachments.filter(att => att.file_type === 'image')
})

const otherAttachments = computed(() => {
  if (!currentRecord.value?.attachments) return []
  return currentRecord.value.attachments.filter(att => att.file_type !== 'image')
})

// 获取类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    '会议参与': 'primary',
    '实地调研': 'success',
    '走访群众': 'warning',
    '意见建议': 'info',
    '监督检查': 'danger',
    '议案提交': 'primary',
    '建议办理': 'success',
    '法律宣传': 'warning',
    '信访接待': 'info',
    '培训学习': 'primary',
    '联络活动': 'success',
    '专题调研': 'warning',
    '视察活动': 'info',
    '座谈交流': 'primary',
    '执法检查': 'danger',
    '民生走访': 'warning',
    '政策宣讲': 'info',
    '其他活动': ''
  }
  return colorMap[type] || ''
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    '已完成': 'success',
    '进行中': 'warning',
    '已暂停': 'info'
  }
  return colorMap[status] || ''
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 显示新增对话框
const showAddDialog = () => {
  dialogMode.value = 'add'
  resetForm()
  dialogVisible.value = true
}

// 编辑记录
const editRecord = async (row) => {
  try {
    loading.value = true
    console.log('开始编辑记录:', row.id)
    const response = await performanceAPI.getRecordDetail(row.id)
    console.log('获取记录详情响应:', response.data)
    console.log('记录的附件数据:', response.data.attachments)
    
    dialogMode.value = 'edit'
    Object.assign(recordForm, {
      ...response.data,
      attachments: response.data.attachments || []
    })
    
    console.log('设置表单数据:', {
      id: recordForm.id,
      attachments: recordForm.attachments,
      attachmentCount: recordForm.attachments.length
    })
    
    dialogVisible.value = true
  } catch (error) {
    console.error('获取记录详情失败:', error)
    ElMessage.error('获取记录详情失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查看记录
const viewRecord = async (row) => {
  try {
    loading.value = true
    const response = await performanceAPI.getRecordDetail(row.id)
    currentRecord.value = response.data

    // 自动加载图片预览
    if (currentRecord.value.attachments) {
      await loadImagePreviews(currentRecord.value.attachments)
    }

    viewDialogVisible.value = true
  } catch (error) {
    console.error('获取记录详情失败:', error)
    ElMessage.error('获取记录详情失败，请重试')
  } finally {
    loading.value = false
  }
}

// 删除记录
const deleteRecord = async (row) => {
  try {
    await ElMessageBox.confirm(
      '此操作将永久删除该履职记录及其相关附件，是否继续？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    await performanceAPI.deleteRecord(row.id)
    ElMessage.success('删除成功')
    loadRecords()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除记录失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(recordForm, {
    id: null,
    performance_date: '',
    performance_type: '',
    performance_content: '',
    activity_location: '',
    detailed_description: '',
    performance_status: '已完成',
    attachments: []
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 保存记录
const saveRecord = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    saving.value = true
    
    const recordData = {
      performance_date: recordForm.performance_date,
      performance_type: recordForm.performance_type,
      performance_content: recordForm.performance_content,
      activity_location: recordForm.activity_location,
      detailed_description: recordForm.detailed_description,
      performance_status: recordForm.performance_status
    }

    if (dialogMode.value === 'add') {
      const response = await performanceAPI.createRecord(recordData)
      ElMessage.success('履职记录添加成功')
      // 如果是新增，可以继续编辑以添加附件
      recordForm.id = response.data.id
      dialogMode.value = 'edit'
    } else {
      await performanceAPI.updateRecord(recordForm.id, recordData)
      ElMessage.success('履职记录更新成功')
      dialogVisible.value = false
    }

    loadRecords()
  } catch (error) {
    console.error('保存记录失败:', error)
    
    // 处理后端返回的详细错误信息
    let errorMessage = '保存失败，请重试'
    
    if (error.response && error.response.data) {
      const errorData = error.response.data
      
      // 处理DRF标准的字段验证错误格式
      if (typeof errorData === 'object' && !errorData.detail && !errorData.message) {
        const errorMessages = []
        
        // 遍历所有字段错误
        Object.keys(errorData).forEach(fieldName => {
          const fieldErrors = errorData[fieldName]
          const displayName = getFieldDisplayName(fieldName)
          
          if (Array.isArray(fieldErrors)) {
            fieldErrors.forEach(errorMsg => {
              errorMessages.push(`${displayName}：${errorMsg}`)
            })
          } else if (typeof fieldErrors === 'string') {
            errorMessages.push(`${displayName}：${fieldErrors}`)
          }
        })
        
        if (errorMessages.length > 0) {
          errorMessage = errorMessages.join('；')
        }
      }
      // 处理直接的错误消息
      else if (errorData.detail) {
        errorMessage = errorData.detail
      }
      else if (errorData.message) {
        errorMessage = errorData.message
      }
      // 处理非字段错误
      else if (errorData.non_field_errors) {
        if (Array.isArray(errorData.non_field_errors)) {
          errorMessage = errorData.non_field_errors.join('；')
        } else {
          errorMessage = errorData.non_field_errors
        }
      }
    }
    
    ElMessage.error(errorMessage)
  } finally {
    saving.value = false
  }
}

// 获取字段显示名称
const getFieldDisplayName = (fieldName) => {
  const fieldNameMap = {
    'performance_date': '履职日期',
    'performance_type': '履职类型',
    'performance_content': '履职内容',
    'activity_location': '活动地点',
    'detailed_description': '详细描述',
    'performance_status': '履职状态'
  }
  return fieldNameMap[fieldName] || fieldName
}

// 搜索处理
const handleSearch = () => {
  console.log('触发搜索，当前搜索条件:', searchForm)
  currentPage.value = 1
  loadRecords()
}

// 重置搜索
const resetSearch = () => {
  console.log('重置搜索条件')
  Object.assign(searchForm, {
    performance_type: '',
    performance_status: '',
    dateRange: [],
    search: ''
  })
  currentPage.value = 1
  loadRecords()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadRecords()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadRecords()
}

// 附件变更处理
const handleAttachmentChange = (attachments) => {
  console.log('附件变更:', attachments)
  // 更新记录的附件状态
  loadRecords()
}

// 上传成功处理
const handleUploadSuccess = (data) => {
  console.log('上传成功:', data)
  ElMessage.success('附件上传成功')
}

// 上传失败处理
const handleUploadError = (error) => {
  console.error('上传失败:', error)
  ElMessage.error('附件上传失败')
}

// 下载附件
const downloadAttachment = async (attachment) => {
  try {
    ElMessage.info('开始下载附件...')
    await performanceFileAPI.downloadAttachment(attachment.id, attachment.original_filename)
    ElMessage.success('附件下载成功')
  } catch (error) {
    console.error('下载附件失败:', error)
    ElMessage.error('下载附件失败，请重试')
  }
}

// 预览图片
const previewImage = async (attachment) => {
  try {
    // 如果已经有预览URL，直接使用，不重新请求
    if (attachment.previewUrl) {
      previewImageUrl.value = attachment.previewUrl
      imageLoading.value = false // 确保不显示加载状态
      imagePreviewVisible.value = true
      return
    }

    // 如果没有预览URL，才进行网络请求
    previewLoading.value = attachment.id
    imageLoading.value = true

    ElMessage.info('正在加载图片预览...')
    const blobUrl = await performanceFileAPI.getSecureFileBlobUrl(attachment.id)
    previewImageUrl.value = blobUrl
    attachment.previewUrl = blobUrl // 缓存URL避免重复请求
    imageLoading.value = false
    imagePreviewVisible.value = true
  } catch (error) {
    console.error('获取图片预览失败:', error)
    ElMessage.error('获取图片预览失败，请重试')
    imageLoading.value = false
  } finally {
    previewLoading.value = null
  }
}

// 图片加载成功处理
const onImageLoadSuccess = () => {
  imageLoading.value = false
}

// 图片加载失败处理
const onImageLoadError = () => {
  imageLoading.value = false
  ElMessage.error('图片加载失败')
}

// 单个图片加载失败处理
const onImageError = (attachment) => {
  attachment.loading = false
  attachment.previewUrl = null
}

// 加载图片预览
const loadImagePreviews = async (attachments) => {
  const imageAttachments = attachments.filter(att => att.file_type === 'image')

  for (const attachment of imageAttachments) {
    try {
      attachment.loading = true
      const blobUrl = await performanceFileAPI.getSecureFileBlobUrl(attachment.id)
      attachment.previewUrl = blobUrl
      attachment.loading = false
    } catch (error) {
      console.error(`加载图片预览失败 ${attachment.id}:`, error)
      attachment.loading = false
      attachment.previewUrl = null
    }
  }
}

// 清理预览资源（只清理预览对话框的URL，不清理缩略图的URL）
const cleanupPreview = () => {
  // 注意：不要清理previewImageUrl，因为它可能和缩略图共用同一个URL
  previewImageUrl.value = ''
  imageLoading.value = false
}

// 清理所有图片预览资源
const cleanupAllImagePreviews = () => {
  if (currentRecord.value?.attachments) {
    currentRecord.value.attachments.forEach(attachment => {
      if (attachment.previewUrl && attachment.previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(attachment.previewUrl)
        attachment.previewUrl = null
      }
    })
  }
}

// 监听预览对话框关闭，清理资源
watch(imagePreviewVisible, (newVal) => {
  if (!newVal) {
    cleanupPreview()
  }
})

// 监听查看对话框关闭，清理所有图片资源
watch(viewDialogVisible, (newVal) => {
  if (!newVal) {
    cleanupAllImagePreviews()
  }
})

// 加载履职记录
const loadRecords = async () => {
  try {
    loading.value = true
    console.log('开始加载履职记录，搜索参数:', searchParams.value)
    
    const response = await performanceAPI.getRecordsList(searchParams.value)
    console.log('履职记录API响应:', response)
    
    recordsList.value = response.data.results || []
    total.value = response.data.count || 0
    
    console.log('履职记录加载成功:', {
      records: recordsList.value.length,
      total: total.value,
      firstRecord: recordsList.value[0]
    })
  } catch (error) {
    console.error('加载履职记录失败:', error)
    console.log('履职记录加载错误详情:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    })
    ElMessage.error('加载数据失败，请重试')
  } finally {
    loading.value = false
  }
}

// 加载选择项数据
const loadChoices = async () => {
  try {
    console.log('开始加载选择项数据...')
    const [typesResponse, statusResponse] = await Promise.all([
      performanceChoicesAPI.getPerformanceTypes(),
      performanceChoicesAPI.getPerformanceStatus()
    ])
    
    console.log('选择项API响应:', { typesResponse, statusResponse })
    
    // 后端返回的格式是 {choices: [...]}，需要提取choices字段
    performanceTypes.value = typesResponse.data?.choices || []
    performanceStatus.value = statusResponse.data?.choices || []
    
    console.log('选择项数据加载成功:', {
      types: performanceTypes.value,
      status: performanceStatus.value
    })
  } catch (error) {
    console.error('加载选择项失败:', error)
    console.log('选择项加载错误详情:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    })
  }
  
  // 无论API是否成功，都确保有默认值
  if (!performanceTypes.value || performanceTypes.value.length === 0) {
    console.log('设置默认履职类型选项')
    performanceTypes.value = [
      { value: '视察调研', label: '视察调研' },
      { value: '学习培训', label: '学习培训' },
      { value: '接待走访', label: '接待走访' },
      { value: '执法检查', label: '执法检查' },
      { value: '主题活动', label: '主题活动' },
      { value: '述职', label: '述职' },
      { value: '法规征询意见', label: '法规征询意见' },
      { value: '政策法规宣传', label: '政策法规宣传' },
      { value: '议案建议办理督办', label: '议案建议办理督办' },
      { value: '会议', label: '会议' },
      { value: '其它', label: '其它' }
    ]
  }
  
  if (!performanceStatus.value || performanceStatus.value.length === 0) {
    console.log('设置默认履职状态选项')
    performanceStatus.value = [
      { value: '进行中', label: '进行中' },
      { value: '已完成', label: '已完成' },
      { value: '已暂停', label: '已暂停' }
    ]
  }
  
  console.log('最终选择项数据:', {
    types: performanceTypes.value.length,
    status: performanceStatus.value.length
  })
}

// 初始化
onMounted(() => {
  loadChoices()
  loadRecords()
})

// 对话框关闭处理
const handleDialogClose = () => {
  resetForm()
}
</script>

<style scoped>
.records-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
}

.page-header {
  flex-shrink: 0;
  margin-bottom: 20px;
}

.page-header h2 {
  color: var(--china-red, #c41e3a);
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.search-card {
  flex-shrink: 0;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.toolbar {
  flex-shrink: 0;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

/* 表格卡片容器 - 占据剩余空间并允许内部滚动 */
.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 表格卡片内容区域 */
:deep(.table-card .el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow: hidden;
}

/* 表格容器 */
:deep(.table-card .el-table) {
  flex: 1;
  overflow: hidden;
}

/* 表格包装器 */
:deep(.table-card .el-table__wrapper) {
  height: 100%;
  overflow: hidden;
}

/* 表格主体区域 */
:deep(.table-card .el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto;
}

/* 分页区域 */
.pagination {
  flex-shrink: 0;
  margin-top: 20px;
  text-align: right;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

.attachment-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.attachment-tip {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.record-detail {
  line-height: 1.6;
}

.content-text,
.description-text {
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.attachments-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.attachments-section h4 {
  margin-bottom: 15px;
  color: var(--china-red, #c41e3a);
  font-size: 16px;
}

.attachments-section h5 {
  margin: 15px 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

/* 图片展示区域 */
.images-section {
  margin-bottom: 20px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.image-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  transition: all 0.3s ease;
}

.image-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #c0c4cc;
}

.image-container {
  width: 100%;
  height: 150px;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.attachment-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-container:hover .attachment-image {
  transform: scale(1.05);
}

.image-loading,
.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
}

.image-info {
  padding: 10px;
}

.image-name {
  font-size: 12px;
  color: #303133;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-actions {
  display: flex;
  justify-content: center;
}

/* 其他附件列表 */
.other-attachments {
  margin-top: 15px;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  background: #fafafa;
}

.attachment-icon {
  margin-right: 12px;
  font-size: 20px;
}

.attachment-info {
  flex: 1;
  min-width: 0;
}

.attachment-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.attachment-size {
  font-size: 12px;
  color: #909399;
}

.attachment-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 图片预览对话框样式 */
.preview-container {
  text-align: center;
  position: relative;
}

.loading-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .records-container {
    padding: 10px;
    height: 100vh;
  }
  
  .search-form {
    display: block;
  }
  
  .search-form .el-form-item {
    display: block;
    margin-bottom: 15px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: center;
  }
  
  .attachments-grid {
    grid-template-columns: 1fr;
  }
  
  /* 移动端隐藏部分列 */
  :deep(.el-table .el-table__cell:nth-child(4)),
  :deep(.el-table .el-table__cell:nth-child(5)) {
    display: none;
  }
  
  /* 移动端表格滚动优化 */
  :deep(.table-card .el-table) {
    font-size: 14px;
  }
  
  :deep(.table-card .el-table .el-button) {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* 操作按钮组样式 */
.action-buttons {
  display: flex;
  flex-direction: row;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
  margin: 0;
}

/* 主题色彩变量 */
:root {
  --china-red: #c41e3a;
  --text-color: #303133;
  --text-color-regular: #606266;
  --text-color-secondary: #909399;
  --border-color: #dcdfe6;
  --border-color-light: #e4e7ed;
  --background-color: #f5f7fa;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: var(--text-color);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

:deep(.el-table td) {
  border-bottom: 1px solid var(--border-color-light);
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 表格滚动条样式优化 */
:deep(.table-card .el-table .el-scrollbar__wrap) {
  overflow-x: auto;
}

:deep(.table-card .el-table .el-table__body-wrapper) {
  overflow-y: auto;
  max-height: none;
}

/* 自定义滚动条样式 */
:deep(.table-card .el-table .el-table__body-wrapper)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:deep(.table-card .el-table .el-table__body-wrapper)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.table-card .el-table .el-table__body-wrapper)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.table-card .el-table .el-table__body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid var(--border-color-light);
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid var(--border-color-light);
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  color: var(--text-color);
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: var(--china-red);
  border-color: var(--china-red);
}

:deep(.el-button--primary:hover) {
  background-color: #d73851;
  border-color: #d73851;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 卡片样式优化 */
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__header) {
  background-color: #fafafa;
  border-bottom: 1px solid var(--border-color-light);
  font-weight: 600;
  color: var(--text-color);
}
</style> 