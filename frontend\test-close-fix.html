<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片预览关闭修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .image-item {
            border: 1px solid #ebeef5;
            border-radius: 8px;
            overflow: hidden;
            background: #fff;
            margin-bottom: 15px;
            width: 200px;
            display: inline-block;
            margin-right: 15px;
        }
        
        .image-container {
            width: 100%;
            height: 150px;
            cursor: pointer;
            overflow: hidden;
            background: #f5f7fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .attachment-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-info {
            padding: 10px;
            text-align: center;
        }
        
        .preview-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .preview-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            max-height: 80vh;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 600px;
            object-fit: contain;
        }
        
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }
        
        .log {
            margin-top: 20px;
            padding: 15px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-item {
            margin: 5px 0;
            font-size: 14px;
        }
        
        .log-item.error {
            color: #dc2626;
            font-weight: bold;
        }
        
        .log-item.success {
            color: #059669;
        }
        
        .log-item.info {
            color: #0369a1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>图片预览关闭修复测试</h2>
        <p>测试点击图片预览后关闭是否还会提示"图片加载失败"</p>
        
        <div class="image-item">
            <div class="image-container" onclick="previewImage('test1')">
                <img 
                    src="https://via.placeholder.com/200x150/67C23A/ffffff?text=测试图片1" 
                    alt="测试图片1"
                    class="attachment-image"
                />
            </div>
            <div class="image-info">
                <div>测试图片1.jpg</div>
            </div>
        </div>
        
        <div class="image-item">
            <div class="image-container" onclick="previewImage('test2')">
                <img 
                    src="https://via.placeholder.com/200x150/409EFF/ffffff?text=测试图片2" 
                    alt="测试图片2"
                    class="attachment-image"
                />
            </div>
            <div class="image-info">
                <div>测试图片2.jpg</div>
            </div>
        </div>
        
        <div class="log">
            <h4>操作日志</h4>
            <div id="logContainer"></div>
        </div>
    </div>
    
    <!-- 图片预览模态框 -->
    <div id="previewModal" class="preview-modal">
        <div class="preview-content">
            <button class="close-btn" onclick="closePreview()">&times;</button>
            <h3>图片预览</h3>
            <img id="previewImage" class="preview-image" alt="预览图片" 
                 onload="onImageLoad()" 
                 onerror="onImageError()">
        </div>
    </div>
    
    <script>
        const imageCache = {};
        let isPreviewVisible = false;
        
        function log(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const item = document.createElement('div');
            item.className = `log-item ${type}`;
            item.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(item);
            container.scrollTop = container.scrollHeight;
        }
        
        function previewImage(imageId) {
            const modal = document.getElementById('previewModal');
            const img = document.getElementById('previewImage');
            
            const imageUrl = `https://via.placeholder.com/800x600/67C23A/ffffff?text=${imageId}大图`;
            
            // 检查缓存
            if (imageCache[imageId]) {
                log(`使用缓存图片: ${imageId}`, 'success');
                img.src = imageCache[imageId];
                isPreviewVisible = true;
                modal.style.display = 'block';
                return;
            }
            
            log(`首次加载图片: ${imageId}`, 'info');
            imageCache[imageId] = imageUrl;
            img.src = imageUrl;
            isPreviewVisible = true;
            modal.style.display = 'block';
        }
        
        function closePreview() {
            const modal = document.getElementById('previewModal');
            isPreviewVisible = false;
            modal.style.display = 'none';
            
            log('预览对话框已关闭', 'info');
            
            // 延迟清理，避免触发错误事件
            setTimeout(() => {
                // 这里我们不清空src，避免触发error事件
                log('延迟清理完成（图片URL保持不变）', 'success');
            }, 300);
        }
        
        function onImageLoad() {
            if (isPreviewVisible) {
                log('图片加载成功', 'success');
            }
        }
        
        function onImageError() {
            // 只有在预览对话框打开且有URL时才显示错误
            const img = document.getElementById('previewImage');
            if (isPreviewVisible && img.src) {
                log('图片加载失败！', 'error');
            } else {
                log('图片错误事件被忽略（对话框已关闭或无URL）', 'info');
            }
        }
        
        // 点击背景关闭
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePreview();
            }
        });
        
        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isPreviewVisible) {
                closePreview();
            }
        });
        
        log('页面加载完成，可以开始测试', 'success');
    </script>
</body>
</html>
