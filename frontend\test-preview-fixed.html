<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片预览功能测试 - 修复版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        /* 图片展示区域 */
        .images-section {
            margin-bottom: 20px;
        }
        
        .images-section h5 {
            margin: 15px 0 10px 0;
            color: #303133;
            font-size: 14px;
            font-weight: 600;
        }
        
        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .image-item {
            border: 1px solid #ebeef5;
            border-radius: 8px;
            overflow: hidden;
            background: #fff;
            transition: all 0.3s ease;
        }
        
        .image-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #c0c4cc;
        }
        
        .image-container {
            width: 100%;
            height: 150px;
            position: relative;
            cursor: pointer;
            overflow: hidden;
            background: #f5f7fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .attachment-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .image-container:hover .attachment-image {
            transform: scale(1.05);
        }
        
        .image-info {
            padding: 10px;
        }
        
        .image-name {
            font-size: 12px;
            color: #303133;
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .image-actions {
            display: flex;
            justify-content: center;
        }
        
        .btn {
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .preview-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .preview-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            max-height: 80vh;
            overflow: auto;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 600px;
            object-fit: contain;
        }
        
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }
        
        .request-log {
            margin-top: 20px;
            padding: 15px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
        }
        
        .request-log h4 {
            margin: 0 0 10px 0;
            color: #0369a1;
        }
        
        .log-item {
            margin: 5px 0;
            font-size: 14px;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>履职记录详情 - 图片预览功能测试（修复版）</h2>
        
        <div class="images-section">
            <h5>现场照片</h5>
            <div class="images-grid">
                <div class="image-item">
                    <div class="image-container" onclick="previewImage('image1', 'https://via.placeholder.com/800x600/67C23A/ffffff?text=现场照片1')">
                        <img 
                            src="https://via.placeholder.com/200x150/67C23A/ffffff?text=现场照片1" 
                            alt="现场照片1.jpg"
                            class="attachment-image"
                        />
                    </div>
                    <div class="image-info">
                        <div class="image-name">现场照片1.jpg</div>
                        <div class="image-actions">
                            <button class="btn" onclick="downloadFile('现场照片1.jpg')">下载</button>
                        </div>
                    </div>
                </div>
                
                <div class="image-item">
                    <div class="image-container" onclick="previewImage('image2', 'https://via.placeholder.com/600x400/409EFF/ffffff?text=活动记录')">
                        <img 
                            src="https://via.placeholder.com/200x150/409EFF/ffffff?text=活动记录" 
                            alt="活动记录.png"
                            class="attachment-image"
                        />
                    </div>
                    <div class="image-info">
                        <div class="image-name">活动记录.png</div>
                        <div class="image-actions">
                            <button class="btn" onclick="downloadFile('活动记录.png')">下载</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="request-log">
            <h4>网络请求日志</h4>
            <div id="requestLog"></div>
        </div>
    </div>
    
    <!-- 图片预览模态框 -->
    <div id="previewModal" class="preview-modal">
        <div class="preview-content">
            <button class="close-btn" onclick="closePreview()">&times;</button>
            <h3>图片预览</h3>
            <img id="previewImage" class="preview-image" alt="预览图片">
        </div>
    </div>
    
    <script>
        // 模拟已加载的图片URL缓存
        const imageCache = {};
        let requestCount = 0;
        
        function logRequest(message) {
            requestCount++;
            const logDiv = document.getElementById('requestLog');
            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            logItem.innerHTML = `[${requestCount}] ${new Date().toLocaleTimeString()} - ${message}`;
            logDiv.appendChild(logItem);
        }
        
        function previewImage(imageId, imageUrl) {
            const modal = document.getElementById('previewModal');
            const img = document.getElementById('previewImage');
            
            // 检查是否已经缓存了这个图片URL
            if (imageCache[imageId]) {
                logRequest(`使用缓存的图片URL: ${imageId} (无网络请求)`);
                img.src = imageCache[imageId];
                modal.style.display = 'block';
                return;
            }
            
            // 模拟网络请求
            logRequest(`发起网络请求获取图片: ${imageId}`);
            
            // 模拟网络延迟
            setTimeout(() => {
                imageCache[imageId] = imageUrl; // 缓存URL
                img.src = imageUrl;
                modal.style.display = 'block';
                logRequest(`图片加载完成: ${imageId}`);
            }, 500);
        }
        
        function closePreview() {
            const modal = document.getElementById('previewModal');
            modal.style.display = 'none';
        }
        
        function downloadFile(filename) {
            logRequest(`下载文件: ${filename}`);
            alert('下载文件: ' + filename);
        }
        
        // 点击模态框背景关闭
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePreview();
            }
        });
        
        // ESC键关闭预览
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePreview();
            }
        });
        
        // 页面加载时模拟初始图片加载
        window.onload = function() {
            logRequest('页面加载完成，缩略图已显示（使用较小尺寸的图片）');
        };
    </script>
</body>
</html>
